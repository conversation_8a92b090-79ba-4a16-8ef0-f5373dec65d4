<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;

class IpWhitelistController extends Controller
{
    public function index()
    {
        $admin = Session::get('admin_user');
        
        // Get all whitelisted IPs
        $whitelistedIps = DB::table('ip_whitelist')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.ip-whitelist.index', compact('admin', 'whitelistedIps'));
    }

    public function store(Request $request)
    {
        $admin = Session::get('admin_user');

        $request->validate([
            'ip_address' => 'required|ip|unique:ip_whitelist,ip_address',
            'description' => 'required|string|max:255',
        ]);

        // Check if IP is currently banned
        $isBanned = DB::table('banned_ips')
            ->where('ip_address', $request->ip_address)
            ->where('is_active', true)
            ->exists();

        if ($isBanned) {
            return redirect()->back()->withErrors([
                'ip_address' => 'IP này hiện đang bị cấm. Vui lòng gỡ ban trước khi thêm vào whitelist.'
            ]);
        }

        // Add to whitelist
        DB::table('ip_whitelist')->insert([
            'ip_address' => $request->ip_address,
            'description' => $request->description,
            'added_by' => $admin['id'],
            'added_by_username' => $admin['username'],
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Log admin action
        $this->logAdminAction(
            $admin,
            'add_ip_whitelist',
            'ip_whitelist',
            $request->ip_address,
            $request->ip_address,
            [],
            ['ip_address' => $request->ip_address, 'description' => $request->description],
            'Thêm IP vào whitelist: ' . $request->description,
            $request->ip()
        );

        return redirect()->route('admin.ip-whitelist.index')
            ->with('success', "Đã thêm IP {$request->ip_address} vào whitelist thành công.");
    }

    public function destroy($id)
    {
        $admin = Session::get('admin_user');

        // Get whitelist entry
        $whitelist = DB::table('ip_whitelist')->where('id', $id)->first();
        if (!$whitelist) {
            return redirect()->back()->withErrors(['error' => 'Không tìm thấy IP whitelist.']);
        }

        // Remove from whitelist
        DB::table('ip_whitelist')->where('id', $id)->delete();

        // Log admin action
        $this->logAdminAction(
            $admin,
            'remove_ip_whitelist',
            'ip_whitelist',
            $id,
            $whitelist->ip_address,
            ['ip_address' => $whitelist->ip_address, 'description' => $whitelist->description],
            [],
            'Xóa IP khỏi whitelist',
            request()->ip()
        );

        return redirect()->route('admin.ip-whitelist.index')
            ->with('success', "Đã xóa IP {$whitelist->ip_address} khỏi whitelist thành công.");
    }

    public function toggle($id)
    {
        $admin = Session::get('admin_user');

        // Get whitelist entry
        $whitelist = DB::table('ip_whitelist')->where('id', $id)->first();
        if (!$whitelist) {
            return redirect()->back()->withErrors(['error' => 'Không tìm thấy IP whitelist.']);
        }

        $newStatus = !$whitelist->is_active;

        // Update status
        DB::table('ip_whitelist')
            ->where('id', $id)
            ->update([
                'is_active' => $newStatus,
                'updated_at' => now()
            ]);

        // Log admin action
        $this->logAdminAction(
            $admin,
            $newStatus ? 'enable_ip_whitelist' : 'disable_ip_whitelist',
            'ip_whitelist',
            $id,
            $whitelist->ip_address,
            ['is_active' => $whitelist->is_active],
            ['is_active' => $newStatus],
            $newStatus ? 'Kích hoạt IP whitelist' : 'Vô hiệu hóa IP whitelist',
            request()->ip()
        );

        $statusText = $newStatus ? 'kích hoạt' : 'vô hiệu hóa';
        return redirect()->route('admin.ip-whitelist.index')
            ->with('success', "Đã {$statusText} IP {$whitelist->ip_address} trong whitelist.");
    }

    public function bulkAdd(Request $request)
    {
        $admin = Session::get('admin_user');

        $request->validate([
            'ip_list' => 'required|string',
            'description' => 'required|string|max:255',
        ]);

        $ipList = array_filter(array_map('trim', explode("\n", $request->ip_list)));
        $addedCount = 0;
        $errors = [];

        foreach ($ipList as $ip) {
            // Validate IP
            if (!filter_var($ip, FILTER_VALIDATE_IP)) {
                $errors[] = "IP không hợp lệ: {$ip}";
                continue;
            }

            // Check if already exists
            $exists = DB::table('ip_whitelist')->where('ip_address', $ip)->exists();
            if ($exists) {
                $errors[] = "IP đã tồn tại trong whitelist: {$ip}";
                continue;
            }

            // Check if banned
            $isBanned = DB::table('banned_ips')
                ->where('ip_address', $ip)
                ->where('is_active', true)
                ->exists();

            if ($isBanned) {
                $errors[] = "IP hiện đang bị cấm: {$ip}";
                continue;
            }

            // Add to whitelist
            DB::table('ip_whitelist')->insert([
                'ip_address' => $ip,
                'description' => $request->description,
                'added_by' => $admin['id'],
                'added_by_username' => $admin['username'],
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            $addedCount++;
        }

        // Log admin action
        $this->logAdminAction(
            $admin,
            'bulk_add_ip_whitelist',
            'ip_whitelist',
            null,
            'bulk_operation',
            [],
            ['added_count' => $addedCount, 'total_ips' => count($ipList)],
            "Thêm hàng loạt {$addedCount} IP vào whitelist",
            $request->ip()
        );

        $message = "Đã thêm {$addedCount} IP vào whitelist thành công.";
        if (!empty($errors)) {
            $message .= " Có " . count($errors) . " lỗi: " . implode(', ', array_slice($errors, 0, 3));
            if (count($errors) > 3) {
                $message .= "...";
            }
        }

        return redirect()->route('admin.ip-whitelist.index')->with('success', $message);
    }

    private function logAdminAction($admin, $action, $targetType, $targetId, $targetName, $oldData, $newData, $reason, $ipAddress)
    {
        DB::table('admin_action_logs')->insert([
            'admin_id' => $admin['id'],
            'admin_username' => $admin['username'],
            'action' => $action,
            'target_type' => $targetType,
            'target_id' => $targetId,
            'target_name' => $targetName,
            'old_data' => json_encode($oldData),
            'new_data' => json_encode($newData),
            'reason' => $reason,
            'ip_address' => $ipAddress,
            'user_agent' => request()->header('User-Agent'),
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }
}
