<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\AdminUser;
use App\Services\IpLogService;

class AdminAuthController extends Controller
{
    /**
     * Admin login
     */
    public function login(Request $request)
    {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        $ipAddress = $request->ip();
        $userAgent = $request->userAgent();

        try {
            // Check if IP is banned
            if (IpLogService::isIpBanned($ipAddress)) {
                IpLogService::logBannedIpAttempt(
                    $ipAddress,
                    $request->username,
                    $userAgent,
                    'admin.login'
                );

                return response()->json([
                    'success' => false,
                    'message' => 'IP của bạn đã bị cấm truy cập'
                ], 403);
            }

            // Find admin user by username
            $user = AdminUser::where('username', $request->username)->first();

            if (!$user) {
                // Log failed login attempt
                IpLogService::logActivity(
                    $ipAddress,
                    null,
                    $request->username,
                    null,
                    'admin_failed_login',
                    'failed',
                    $userAgent
                );

                return response()->json([
                    'success' => false,
                    'message' => 'Tài khoản không tồn tại'
                ], 401);
            }

            // Check password (Laravel Hash)
            if (!Hash::check($request->password, $user->password)) {
                // Log failed login attempt
                IpLogService::logActivity(
                    $ipAddress,
                    $user->id,
                    $user->username,
                    null,
                    'admin_failed_login',
                    'failed',
                    $userAgent
                );

                return response()->json([
                    'success' => false,
                    'message' => 'Mật khẩu không chính xác'
                ], 401);
            }

            // Check if user is active
            if (!$user->isActive()) {
                // Log unauthorized access attempt
                IpLogService::logActivity(
                    $ipAddress,
                    $user->id,
                    $user->username,
                    null,
                    'admin_unauthorized',
                    'failed',
                    $userAgent
                );

                return response()->json([
                    'success' => false,
                    'message' => 'Tài khoản đã bị khóa'
                ], 403);
            }

            // Update last login info
            $user->update([
                'last_login_at' => now(),
                'last_login_ip' => $ipAddress
            ]);

            // Log successful admin login
            IpLogService::logActivity(
                $ipAddress,
                $user->id,
                $user->username,
                null,
                'admin_login',
                'success',
                $userAgent
            );

            // Run advanced threat detection
            IpLogService::detectAdvancedThreats($ipAddress, $user->id, $user->username);

            // Create token
            $token = $user->createToken('admin-token')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => 'Đăng nhập thành công',
                'token' => $token,
                'user' => [
                    'id' => $user->id,
                    'username' => $user->username,
                    'email' => $user->email,
                    'full_name' => $user->full_name,
                    'role' => $user->role,
                    'permissions' => $user->permissions,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Admin logout
     */
    public function logout(Request $request)
    {
        try {
            $user = $request->user();

            // Log admin logout
            IpLogService::logActivity(
                $request->ip(),
                $user->id,
                $user->username,
                null,
                'admin_logout',
                'success',
                $request->userAgent()
            );

            $request->user()->currentAccessToken()->delete();

            return response()->json([
                'success' => true,
                'message' => 'Đăng xuất thành công'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi đăng xuất'
            ], 500);
        }
    }

    /**
     * Get current admin user
     */
    public function user(Request $request)
    {
        try {
            $user = $request->user();

            return response()->json([
                'success' => true,
                'user' => [
                    'UserID' => $user->UserID,
                    'UserName' => $user->UserName,
                    'Email' => $user->Email,
                    'groupid' => $user->groupid,
                    'Money' => $user->Money,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi lấy thông tin user'
            ], 500);
        }
    }
}
