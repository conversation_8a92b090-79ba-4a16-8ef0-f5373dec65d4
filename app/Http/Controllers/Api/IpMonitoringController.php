<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\IpLogService;
use Illuminate\Support\Facades\DB;

class IpMonitoringController extends Controller
{
    /**
     * Get IP monitoring dashboard data
     */
    public function dashboard(Request $request)
    {
        try {
            $days = $request->get('days', 7);

            $data = [
                'stats' => [
                    'total_logins_today' => DB::table('ip_logs')
                        ->whereDate('created_at', today())
                        ->count(),
                    'unique_ips_today' => DB::table('ip_logs')
                        ->whereDate('created_at', today())
                        ->distinct('ip_address')
                        ->count(),
                    'failed_logins_today' => DB::table('ip_logs')
                        ->whereDate('created_at', today())
                        ->whereIn('action', ['failed_login', 'admin_failed_login'])
                        ->count(),
                    'banned_ip_attempts_today' => DB::table('ip_logs')
                        ->whereDate('created_at', today())
                        ->where('action', 'banned_ip_attempt')
                        ->count(),
                ],
                'top_ips' => IpLogService::getTopIps(10, $days),
                'recent_alerts' => IpLogService::getRecentAlerts(10),
                'banned_ip_stats' => IpLogService::getBannedIpAttemptStats($days),
                'recent_activities' => DB::table('ip_logs')
                    ->select('ip_address', 'username', 'action', 'status', 'country', 'city', 'created_at')
                    ->orderBy('created_at', 'desc')
                    ->limit(20)
                    ->get(),
            ];

            return response()->json([
                'success' => true,
                'data' => $data
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get IP statistics for specific IP
     */
    public function ipStats(Request $request, $ipAddress)
    {
        try {
            $stats = IpLogService::getIpStatistics($ipAddress);
            
            $recentLogs = DB::table('ip_logs')
                ->where('ip_address', $ipAddress)
                ->orderBy('created_at', 'desc')
                ->limit(50)
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'ip_address' => $ipAddress,
                    'stats' => $stats,
                    'recent_logs' => $recentLogs
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get security alerts
     */
    public function alerts(Request $request)
    {
        try {
            $status = $request->get('status', 'new');
            $limit = $request->get('limit', 50);

            $alerts = DB::table('ip_alerts')
                ->when($status !== 'all', function($query) use ($status) {
                    return $query->where('status', $status);
                })
                ->orderBy('severity', 'desc')
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();

            return response()->json([
                'success' => true,
                'data' => $alerts
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark alert as resolved
     */
    public function resolveAlert(Request $request, $alertId)
    {
        try {
            DB::table('ip_alerts')
                ->where('id', $alertId)
                ->update([
                    'status' => 'resolved',
                    'resolved_at' => now(),
                    'resolved_by' => $request->user()->UserID,
                    'updated_at' => now()
                ]);

            return response()->json([
                'success' => true,
                'message' => 'Alert đã được đánh dấu là đã xử lý'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Ban IP address
     */
    public function banIp(Request $request)
    {
        $request->validate([
            'ip_address' => 'required|ip',
            'reason' => 'required|string|max:255',
            'expires_at' => 'nullable|date|after:now',
        ]);

        try {
            // Check if IP is already banned
            $existingBan = DB::table('banned_ips')
                ->where('ip_address', $request->ip_address)
                ->where(function($query) {
                    $query->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                })
                ->first();

            if ($existingBan) {
                return response()->json([
                    'success' => false,
                    'message' => 'IP này đã bị cấm rồi'
                ], 400);
            }

            // Ban the IP
            DB::table('banned_ips')->insert([
                'ip_address' => $request->ip_address,
                'reason' => $request->reason,
                'banned_by' => $request->user()->id,
                'banned_at' => now(),
                'expires_at' => $request->expires_at,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'IP đã được cấm thành công'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Unban IP address
     */
    public function unbanIp(Request $request)
    {
        $request->validate([
            'ip_address' => 'required|ip',
        ]);

        try {
            $updated = DB::table('banned_ips')
                ->where('ip_address', $request->ip_address)
                ->where(function($query) {
                    $query->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                })
                ->update([
                    'expires_at' => now(),
                    'unbanned_by' => $request->user()->id,
                    'unbanned_at' => now(),
                    'updated_at' => now()
                ]);

            if ($updated) {
                return response()->json([
                    'success' => true,
                    'message' => 'IP đã được bỏ cấm thành công'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'IP này không bị cấm hoặc đã hết hạn cấm'
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get banned IPs list
     */
    public function bannedIps(Request $request)
    {
        try {
            $bannedIps = DB::table('banned_ips')
                ->leftJoin('admin_users as banned_by_user', 'banned_ips.banned_by', '=', 'banned_by_user.id')
                ->leftJoin('admin_users as unbanned_by_user', 'banned_ips.unbanned_by', '=', 'unbanned_by_user.id')
                ->select(
                    'banned_ips.*',
                    'banned_by_user.username as banned_by_username',
                    'unbanned_by_user.username as unbanned_by_username'
                )
                ->orderBy('banned_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $bannedIps
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }
}
