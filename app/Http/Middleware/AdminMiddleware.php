<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Auth;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $admin = null;

        // First try to get admin from Sanctum token (for API requests)
        if ($request->bearerToken()) {
            $admin = Auth::guard('sanctum')->user();
        }

        // If no token auth, try session (for web requests)
        if (!$admin) {
            $adminSession = Session::get('admin_user');
            if ($adminSession) {
                // Convert session data to object-like structure for consistency
                $admin = (object) $adminSession;
            }
        }

        if (!$admin) {
            // If it's an AJAX request, return JSON
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Vui lòng đăng nhập để tiếp tục.'
                ], 401);
            }

            // Otherwise redirect to login page
            return redirect('/admin/login')->withErrors(['error' => 'Vui lòng đăng nhập để tiếp tục.']);
        }

        // Check if admin has proper role (admin or super_admin)
        $role = is_object($admin) ? $admin->role : $admin['role'];
        if (!in_array($role, ['admin', 'super_admin'])) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn không có quyền truy cập tính năng này.'
                ], 403);
            }

            return redirect('/admin/login')->withErrors(['error' => 'Bạn không có quyền truy cập tính năng này.']);
        }

        return $next($request);
    }
}
