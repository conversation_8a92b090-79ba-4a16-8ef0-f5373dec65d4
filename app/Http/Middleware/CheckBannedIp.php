<?php

namespace App\Http\Middleware;

use App\Services\IpLogService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class CheckBannedIp
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $ipAddress = $request->ip();
        
        // Check if IP is whitelisted first
        if ($this->isIpWhitelisted($ipAddress)) {
            return $next($request);
        }
        
        // Check if IP is banned
        if (IpLogService::isIpBanned($ipAddress)) {
            // Get ban details for logging
            $banDetails = $this->getBanDetails($ipAddress);
            
            // Log the blocked attempt
            Log::warning('Blocked login attempt from banned IP', [
                'ip' => $ipAddress,
                'user_agent' => $request->userAgent(),
                'route' => $request->route() ? $request->route()->getName() : $request->path(),
                'ban_reason' => $banDetails['reason'] ?? 'Unknown',
                'ban_type' => $banDetails['type'] ?? 'Unknown',
                'banned_at' => $banDetails['banned_at'] ?? null,
                'expires_at' => $banDetails['expires_at'] ?? null,
                'attempted_username' => $request->input('username'),
                'timestamp' => now()
            ]);
            
            // Log banned IP attempt with detailed information
            IpLogService::logBannedIpAttempt(
                $ipAddress,
                $request->input('username'),
                $request->userAgent(),
                $request->route() ? $request->route()->getName() : $request->path(),
                $banDetails
            );
            
            // Return appropriate response based on request type
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Địa chỉ IP của bạn đã bị cấm truy cập. Vui lòng liên hệ admin để được hỗ trợ.',
                    'error_code' => 'IP_BANNED'
                ], 403);
            }
            
            // For web requests, redirect back with error
            $errorMessage = 'Địa chỉ IP của bạn đã bị cấm truy cập. Vui lòng liên hệ admin để được hỗ trợ.';
            
            // Add more specific message if temporary ban
            if (isset($banDetails['expires_at']) && $banDetails['expires_at']) {
                $expiresAt = \Carbon\Carbon::parse($banDetails['expires_at']);
                if ($expiresAt->isFuture()) {
                    $errorMessage .= ' Thời gian hết hạn: ' . $expiresAt->format('d/m/Y H:i:s');
                }
            }
            
            return back()->withErrors([
                'ip_banned' => $errorMessage
            ])->withInput($request->except('password'));
        }
        
        return $next($request);
    }
    
    /**
     * Check if IP is whitelisted
     */
    private function isIpWhitelisted($ipAddress): bool
    {
        return DB::table('ip_whitelist')
            ->where('ip_address', $ipAddress)
            ->where('is_active', true)
            ->exists();
    }
    
    /**
     * Get ban details for an IP
     */
    private function getBanDetails($ipAddress): array
    {
        $ban = DB::table('banned_ips')
            ->where('ip_address', $ipAddress)
            ->where('is_active', true)
            ->where(function($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', now());
            })
            ->first();
            
        if (!$ban) {
            return [];
        }
        
        return [
            'reason' => $ban->reason,
            'type' => $ban->type,
            'banned_at' => $ban->banned_at,
            'expires_at' => $ban->expires_at,
            'banned_by' => $ban->banned_by_username ?? 'System'
        ];
    }
}
