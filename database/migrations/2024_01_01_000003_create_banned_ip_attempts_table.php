<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBannedIpAttemptsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('banned_ip_attempts')) {
            Schema::create('banned_ip_attempts', function (Blueprint $table) {
                $table->id();
                $table->string('ip_address', 45)->index();
                $table->string('attempted_username')->nullable();
                $table->text('user_agent')->nullable();
                $table->string('route')->nullable();
                $table->text('ban_reason')->nullable();
                $table->enum('ban_type', ['permanent', 'temporary'])->nullable();
                $table->timestamp('banned_at')->nullable();
                $table->timestamp('expires_at')->nullable();
                $table->string('banned_by')->nullable();
                $table->timestamp('attempt_timestamp');
                $table->timestamps();

                // Indexes for performance
                $table->index(['ip_address', 'created_at']);
                $table->index(['attempted_username', 'created_at']);
                $table->index('attempt_timestamp');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('banned_ip_attempts');
    }
}
