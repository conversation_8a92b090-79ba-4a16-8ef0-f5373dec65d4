<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAccountIdToIpLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ip_logs', function (Blueprint $table) {
            // Add account_id column after ip_address
            $table->unsignedBigInteger('account_id')->nullable()->after('ip_address');
            
            // Add index for performance
            $table->index('account_id');
            $table->index(['account_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ip_logs', function (Blueprint $table) {
            $table->dropIndex(['ip_logs_account_id_index']);
            $table->dropIndex(['ip_logs_account_id_created_at_index']);
            $table->dropColumn('account_id');
        });
    }
}
