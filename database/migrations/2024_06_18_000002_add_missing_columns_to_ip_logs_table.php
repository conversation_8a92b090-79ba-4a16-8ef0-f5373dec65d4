<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToIpLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ip_logs', function (Blueprint $table) {
            // Add missing columns that IpLogService expects
            $table->string('status')->default('success')->after('action');
            $table->string('country', 2)->nullable()->after('user_agent');
            $table->string('city')->nullable()->after('country');
            $table->text('location_data')->nullable()->after('city');
            
            // Add indexes for performance
            $table->index('status');
            $table->index('country');
            $table->index(['ip_address', 'status']);
            $table->index(['ip_address', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ip_logs', function (Blueprint $table) {
            $table->dropIndex(['ip_logs_status_index']);
            $table->dropIndex(['ip_logs_country_index']);
            $table->dropIndex(['ip_logs_ip_address_status_index']);
            $table->dropIndex(['ip_logs_ip_address_created_at_index']);
            
            $table->dropColumn(['status', 'country', 'city', 'location_data']);
        });
    }
}
