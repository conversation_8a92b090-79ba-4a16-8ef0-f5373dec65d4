<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddStatusAndLocationDataToIpLogs extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ip_logs', function (Blueprint $table) {
            // Add status column after action
            $table->string('status')->default('success')->after('action');
            
            // Add location_data column after city
            $table->text('location_data')->nullable()->after('city');
            
            // Add indexes for performance
            $table->index('status');
            $table->index(['ip_address', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ip_logs', function (Blueprint $table) {
            $table->dropIndex(['ip_logs_status_index']);
            $table->dropIndex(['ip_logs_ip_address_status_index']);
            
            $table->dropColumn(['status', 'location_data']);
        });
    }
}
