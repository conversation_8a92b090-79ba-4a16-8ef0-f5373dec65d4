<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class UpdateActionEnumInIpLogs extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Update the action enum to include new admin actions
        DB::statement("ALTER TABLE ip_logs MODIFY COLUMN action ENUM(
            'login',
            'logout', 
            'register',
            'failed_login',
            'admin_login',
            'admin_logout',
            'admin_failed_login',
            'admin_unauthorized',
            'banned_ip_attempt'
        ) NOT NULL");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Revert back to original enum values
        DB::statement("ALTER TABLE ip_logs MODIFY COLUMN action ENUM(
            'login',
            'logout',
            'register', 
            'failed_login'
        ) NOT NULL");
    }
}
