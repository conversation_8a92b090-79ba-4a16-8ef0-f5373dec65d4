<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateIpAlertsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ip_alerts', function (Blueprint $table) {
            $table->id();
            $table->string('ip_address');
            $table->string('alert_type');
            $table->string('title');
            $table->text('description');
            $table->integer('severity')->default(1); // 1=low, 2=medium, 3=high, 4=critical
            $table->text('alert_data')->nullable();
            $table->enum('status', ['new', 'investigating', 'resolved', 'false_positive'])->default('new');
            $table->timestamp('resolved_at')->nullable();
            $table->unsignedBigInteger('resolved_by')->nullable();
            $table->timestamps();

            // Indexes
            $table->index('ip_address');
            $table->index('alert_type');
            $table->index('status');
            $table->index('severity');
            $table->index(['ip_address', 'alert_type']);
            $table->index(['status', 'severity']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ip_alerts');
    }
}
