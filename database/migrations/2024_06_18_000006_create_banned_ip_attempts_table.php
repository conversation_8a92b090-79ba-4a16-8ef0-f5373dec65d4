<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBannedIpAttemptsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('banned_ip_attempts', function (Blueprint $table) {
            $table->id();
            $table->string('ip_address');
            $table->string('attempted_username')->nullable();
            $table->text('user_agent')->nullable();
            $table->string('route')->nullable();
            $table->string('ban_reason')->nullable();
            $table->string('ban_type')->nullable();
            $table->timestamp('banned_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->unsignedBigInteger('banned_by')->nullable();
            $table->timestamp('attempt_timestamp');
            $table->timestamps();

            // Indexes
            $table->index('ip_address');
            $table->index('attempted_username');
            $table->index('attempt_timestamp');
            $table->index('created_at');
            $table->index(['ip_address', 'attempt_timestamp']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('banned_ip_attempts');
    }
}
